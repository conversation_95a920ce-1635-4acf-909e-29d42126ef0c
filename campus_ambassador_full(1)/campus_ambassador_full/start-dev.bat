@echo off
echo 🚀 Starting Campus Ambassador Program Development Environment
echo ============================================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.8+ first.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed

REM Install frontend dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    npm install
)

REM Install backend dependencies
echo 🐍 Installing backend dependencies...
cd backend
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

call venv\Scripts\activate.bat
pip install -r requirements.txt
cd ..

echo 🎯 Starting development servers...

REM Start backend server
echo 🔧 Starting FastAPI backend on port 8000...
cd backend
call venv\Scripts\activate.bat
start "Backend Server" cmd /k "uvicorn main:app --reload --port 8000"
cd ..

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend server
echo ⚡ Starting Next.js frontend on port 3000...
start "Frontend Server" cmd /k "npm run dev"

echo.
echo 🎉 Development environment is ready!
echo ============================================================
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 API Docs: http://localhost:8000/docs
echo.
echo 🔐 Admin Credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo Press any key to exit...
pause >nul
