# 🚀 Quick Start Guide

## Prerequisites
- Node.js 18+ ([Download](https://nodejs.org/))
- Python 3.8+ ([Download](https://python.org/))

## Option 1: Automated Setup (Recommended)

### For Linux/macOS:
```bash
./start-dev.sh
```

### For Windows:
```cmd
start-dev.bat
```

## Option 2: Manual Setup

### 1. Install Frontend Dependencies
```bash
npm install
```

### 2. Setup Backend
```bash
cd backend
python -m venv venv

# Linux/macOS
source venv/bin/activate

# Windows
venv\Scripts\activate

pip install -r requirements.txt
cd ..
```

### 3. Start Development Servers

**Terminal 1 - Backend:**
```bash
cd backend
source venv/bin/activate  # or venv\Scripts\activate on Windows
uvicorn main:app --reload --port 8000
```

**Terminal 2 - Frontend:**
```bash
npm run dev
```

## 🌐 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🔐 Admin Login

- **Username**: `admin`
- **Password**: `admin123`

## 📱 Test the Flow

1. Visit http://localhost:3000
2. Click "Apply Now" to register
3. Fill the form and submit
4. Get your CA ID and referral link
5. Test admin panel at http://localhost:3000/admin

## 🛠️ Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

## 🔧 Environment Variables

Create `.env.local` file:
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

## 📦 Production Build

```bash
npm run build
npm start
```

## 🐛 Troubleshooting

### Port Already in Use
- Frontend (3000): Change in `package.json` dev script
- Backend (8000): Change in uvicorn command

### Database Issues
- Delete `backend/ca_program.db` to reset database
- Restart backend server to recreate tables

### Dependencies Issues
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# Backend
cd backend
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

---

**🎉 You're all set! Happy coding!**
