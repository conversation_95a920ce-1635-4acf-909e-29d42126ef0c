'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, Co<PERSON>, Share2, ArrowLeft, ExternalLink } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CARegistration } from '@/types';

export default function SuccessPage() {
  const [caData, setCaData] = useState<CARegistration | null>(null);

  useEffect(() => {
    const storedData = localStorage.getItem('caData');
    if (storedData) {
      try {
        const data = JSON.parse(storedData);
        setCaData(data);
      } catch (error) {
        console.error('Error parsing CA data:', error);
      }
    }
  }, []);

  const referralLink = caData?.ca_id 
    ? `${window.location.origin}?ref=${caData.ca_id}`
    : '';

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert("Copied to clipboard successfully!");
    } catch (error) {
      alert("Failed to copy to clipboard.");
    }
  };

  const shareReferralLink = async () => {
    if (navigator.share && referralLink) {
      try {
        await navigator.share({
          title: 'Join Secura Trainings Campus Ambassador Program',
          text: 'Join me as a Campus Ambassador at Secura Trainings!',
          url: referralLink,
        });
      } catch (error) {
        // Fallback to copy
        copyToClipboard(referralLink);
      }
    } else {
      copyToClipboard(referralLink);
    }
  };

  if (!caData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="pt-6">
            <p className="text-gray-600">Loading your registration details...</p>
            <Link href="/" className="inline-block mt-4">
              <Button variant="outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl"
      >
        <Card className="shadow-xl">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
            </motion.div>
            
            <div>
              <CardTitle className="text-3xl font-bold text-gray-900">
                Registration Successful!
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 mt-2">
                Welcome to the Secura Trainings Campus Ambassador Program
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* CA Details */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg"
            >
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Your Campus Ambassador Details</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Name</p>
                  <p className="font-medium text-gray-900">{caData.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">CA ID</p>
                  <p className="font-medium text-blue-600 text-lg">{caData.ca_id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Email</p>
                  <p className="font-medium text-gray-900">{caData.email}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Status</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    {caData.status}
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Referral Link */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-gradient-to-r from-orange-50 to-yellow-50 p-6 rounded-lg"
            >
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Your Unique Referral Link</h3>
              <p className="text-gray-600 mb-4">
                Share this link with your friends and earn rewards for every successful registration!
              </p>
              
              <div className="flex items-center space-x-2 p-3 bg-white rounded-lg border">
                <input
                  type="text"
                  value={referralLink}
                  readOnly
                  className="flex-1 bg-transparent text-sm text-gray-700 outline-none"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(referralLink)}
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex gap-3 mt-4">
                <Button
                  onClick={shareReferralLink}
                  className="flex-1"
                  variant="outline"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share Link
                </Button>
                <Button
                  onClick={() => copyToClipboard(referralLink)}
                  className="flex-1"
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy Link
                </Button>
              </div>
            </motion.div>

            {/* Next Steps */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg"
            >
              <h3 className="text-xl font-semibold text-gray-900 mb-4">What's Next?</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                  <span className="text-gray-700">Our team will review your application within 24-48 hours</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                  <span className="text-gray-700">You'll receive an email confirmation once approved</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                  <span className="text-gray-700">Start sharing your referral link to earn rewards</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                  <span className="text-gray-700">Join our exclusive Campus Ambassador community</span>
                </li>
              </ul>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex gap-4"
            >
              <Link href="/" className="flex-1">
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <Link href="/admin" className="flex-1">
                <Button className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Admin Panel
                </Button>
              </Link>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
