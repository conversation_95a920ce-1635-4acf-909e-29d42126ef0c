'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  GraduationCap, 
  Megaphone, 
  DollarSign, 
  Gift, 
  Users,
  ChevronDown,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const features = [
  {
    icon: GraduationCap,
    title: "Represent Our Brand",
    description: "Represent our brand at your college and promote our values."
  },
  {
    icon: Megaphone,
    title: "Unique Referral Link",
    description: "Get a unique referral link to share with friends and peers."
  },
  {
    icon: DollarSign,
    title: "Earn Cash Incentives",
    description: "Earn cash incentives for every successful registration via your link."
  },
  {
    icon: Gift,
    title: "Promotional Perks & Events",
    description: "Receive regular promotional perks and exciting event opportunities."
  },
  {
    icon: Users,
    title: "Network & Connect",
    description: "Network with students and professionals across campuses."
  }
];

const responsibilities = [
  "Promote our programs online and offline",
  "Share the referral link and encourage registrations",
  "Help organize or support online campaigns and events"
];

const rewards = [
  { icon: "💸", text: "Cash incentives for each successful sign-up through your referral link" },
  { icon: "🎖️", text: "Internship Certificate at year-end" },
  { icon: "💰", text: "Additional bonuses for high performers" },
  { icon: "🧢", text: "Branded swag and gifts throughout the year" }
];

const eligibility = [
  "Currently enrolled UG/PG student",
  "Active on social media and campus activities",
  "Committed to a 1-year engagement"
];

const faqs = [
  {
    question: "What is the time commitment for the Campus Ambassador Program?",
    answer: "Ambassadors are expected to actively participate for 1 year, engaging in promotions and events."
  },
  {
    question: "How do I get my unique referral link?",
    answer: "Once accepted, you will be provided with a unique referral link to share with your network."
  },
  {
    question: "Are there any prerequisites to apply?",
    answer: "You must be a currently enrolled UG/PG student, active on campus, and able to commit for 1 year."
  }
];

export default function LandingPage() {
  const searchParams = useSearchParams();

  useEffect(() => {
    const ref = searchParams.get('ref');
    if (ref) {
      localStorage.setItem('referralCode', ref);
      console.log('Referral code stored:', ref);
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Navigation */}
      <nav className="fixed top-0 w-full bg-white/80 backdrop-blur-md border-b border-gray-200 z-50">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="text-2xl font-bold text-blue-600"
          >
            Secura Trainings
          </motion.div>
          <Link href="/admin">
            <Button variant="destructive" size="sm">
              <ExternalLink className="w-4 h-4 mr-2" />
              Admin Login
            </Button>
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
              Become a Campus Ambassador
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 font-medium">
              Promote. Earn. Grow.
            </p>
            <Link href="/register">
              <Button size="lg" className="text-lg px-8 py-6 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                Apply Now
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Program Duration */}
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Program Duration</h2>
            <p className="text-xl text-blue-600 font-semibold">
              Ambassadors are onboarded for a <strong>1-year program</strong>.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Key Features</h2>
          </motion.div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="text-center">
                    <feature.icon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center text-gray-600">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Responsibilities */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Responsibilities</h2>
            <div className="max-w-2xl mx-auto">
              <ul className="space-y-4">
                {responsibilities.map((responsibility, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center text-lg text-gray-700"
                  >
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-4"></div>
                    {responsibility}
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Rewards */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Rewards & Recognition</h2>
            <div className="max-w-3xl mx-auto">
              <div className="grid md:grid-cols-2 gap-6">
                {rewards.map((reward, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg"
                  >
                    <span className="text-2xl mr-4">{reward.icon}</span>
                    <span className="text-gray-700">{reward.text}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Eligibility */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Eligibility Criteria</h2>
            <div className="max-w-2xl mx-auto">
              <ul className="space-y-4">
                {eligibility.map((criteria, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center text-lg text-gray-700"
                  >
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-4"></div>
                    {criteria}
                  </motion.li>
                ))}
              </ul>
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Join?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Apply now to become a part of the Secura Trainings Campus Ambassador Program and start your journey!
            </p>
            <Link href="/register">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-6">
                Apply Now
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Frequently Asked Questions</h2>
            <div className="max-w-3xl mx-auto">
              <Accordion type="single" collapsible className="w-full">
                {faqs.map((faq, index) => (
                  <AccordionItem key={index} value={`item-${index}`}>
                    <AccordionTrigger className="text-left">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-gray-600">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 px-4 bg-gray-900 text-center">
        <div className="container mx-auto">
          <p className="text-gray-400">
            © 2025 Secura Trainings and Compliances. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
