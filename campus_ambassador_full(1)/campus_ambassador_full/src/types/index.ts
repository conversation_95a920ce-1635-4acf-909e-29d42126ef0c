export interface CARegistration {
  id: number;
  name: string;
  email: string;
  mobile: string;
  college: string;
  branch: string;
  year_of_study: string;
  linkedin?: string;
  instagram?: string;
  ca_id?: string;
  status: 'pending' | 'active' | 'rejected';
  referral_code?: string;
  referral_count: number;
  created_at?: string;
}

export interface CARegistrationCreate {
  name: string;
  email: string;
  mobile: string;
  college: string;
  branch: string;
  year_of_study: string;
  linkedin?: string;
  instagram?: string;
  referral_code?: string;
}

export interface CAStats {
  total_cas: number;
  active_cas: number;
  pending_cas: number;
  total_referrals: number;
}

export interface AdminLoginData {
  userid: string;
  password: string;
}
