# Campus Ambassador Program

A modern, full-stack web application for managing Campus Ambassador registrations with referral tracking and admin management. Built with Next.js 13.5.1, React 18, TypeScript, Tailwind CSS, and FastAPI.

## 🚀 Features

- **Modern Landing Page**: Beautiful, responsive design with animations
- **Student Registration**: Form validation with React Hook Form + Zod
- **Success Page**: Unique CA ID and referral link generation with sharing
- **Admin Dashboard**: Full management panel with real-time statistics
- **Referral System**: Automatic tracking and reward calculation
- **Mobile Responsive**: Optimized for all device sizes

## 🛠️ Tech Stack

### Frontend
- **Next.js 13.5.1** with App Router
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Radix UI** components
- **Framer Motion** for animations
- **React Hook Form** + **Zod** for form validation
- **Next SEO** for SEO optimization

### Backend
- **FastAPI** with Python
- **SQLAlchemy** ORM
- **SQLite** database
- **Pydantic** for data validation

## 📁 Project Structure

```
campus_ambassador_full/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── page.tsx         # Landing page
│   │   ├── register/        # Registration page
│   │   ├── success/         # Success page
│   │   ├── admin/           # Admin dashboard
│   │   ├── layout.tsx       # Root layout
│   │   └── globals.css      # Global styles
│   ├── components/
│   │   └── ui/              # Reusable UI components
│   ├── lib/
│   │   ├── api.ts           # API client functions
│   │   ├── utils.ts         # Utility functions
│   │   └── validations.ts   # Zod schemas
│   └── types/               # TypeScript type definitions
├── backend/
│   ├── main.py              # FastAPI backend
│   ├── requirements.txt     # Python dependencies
│   └── ca_program.db        # SQLite database
├── package.json             # Node.js dependencies
├── tailwind.config.js       # Tailwind configuration
├── tsconfig.json            # TypeScript configuration
└── next.config.js           # Next.js configuration
```

## 🛠️ How to Run

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.8+

### 1. Install Frontend Dependencies
```bash
cd campus_ambassador_full
npm install
# or
yarn install
```

### 2. Start Backend Server
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### 3. Start Frontend Development Server
```bash
# In the root directory (campus_ambassador_full)
npm run dev
# or
yarn dev
```

### 4. Access Application
- **Landing Page**: http://localhost:3000
- **Registration**: http://localhost:3000/register
- **Success Page**: http://localhost:3000/success
- **Admin Panel**: http://localhost:3000/admin

## 🔐 Admin Credentials
- **Username**: admin
- **Password**: admin123

## 🎯 Key Features

### Student Flow
1. Visit modern landing page with animations
2. Click "Apply Now" to navigate to registration
3. Fill validated registration form
4. Get unique CA ID and shareable referral link
5. Share referral link via native sharing or copy

### Admin Flow
1. Secure login to admin dashboard
2. View real-time statistics and metrics
3. Approve/reject applications with one click
4. Monitor referral performance
5. Track program growth

### Referral System
- Each CA gets unique referral link: `/?ref=CA001`
- Automatic referral tracking with localStorage
- Real-time referral count updates
- Native sharing API support

## 🔧 Technical Details

### Backend (FastAPI)
- **Port**: 8000
- **Database**: SQLite with SQLAlchemy ORM
- **CORS**: Configured for Next.js development
- **API**: RESTful endpoints with Pydantic validation
- **Features**: Form data support, error handling

### Frontend (Next.js)
- **Port**: 3000 (development)
- **Framework**: Next.js 13.5.1 with App Router
- **Styling**: Tailwind CSS with custom design system
- **Components**: Radix UI primitives
- **Animations**: Framer Motion
- **Forms**: React Hook Form with Zod validation
- **State**: React hooks with localStorage persistence

## 📊 Database Schema

### CA Registrations Table
- **id**: Primary key (Integer)
- **name**: Full name (String, required)
- **email**: Email address (String, unique, required)
- **mobile**: Phone number (String, required)
- **college**: College/University name (String, required)
- **branch**: Department/Branch (String, required)
- **year_of_study**: Academic year (String, required)
- **linkedin**: LinkedIn profile URL (String, optional)
- **instagram**: Instagram handle (String, optional)
- **ca_id**: Unique Campus Ambassador ID (String, auto-generated)
- **status**: Application status (pending/active/rejected)
- **referral_code**: Who referred this CA (String, optional)
- **referral_count**: Number of successful referrals (Integer, default: 0)
- **created_at**: Registration timestamp (String, ISO format)

## 🎨 Design Features
- **Modern UI**: Clean, professional design with gradients
- **Responsive**: Mobile-first approach with Tailwind CSS
- **Animations**: Smooth transitions with Framer Motion
- **Accessibility**: ARIA-compliant components from Radix UI
- **Form Validation**: Real-time validation with helpful error messages
- **Toast Notifications**: User-friendly feedback system
- **Loading States**: Proper loading indicators for better UX

## 🚀 Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Type checking
npm run type-check

# Linting
npm run lint
```

## 🔄 API Endpoints

### Public Endpoints
- `POST /ca/register` - Register new Campus Ambassador
- `POST /register` - Legacy registration endpoint

### Admin Endpoints
- `POST /admin/login` - Admin authentication
- `GET /admin/registrations` - Get all registrations
- `PUT /admin/ca/{ca_id}/status` - Update CA status
- `GET /admin/stats` - Get dashboard statistics

## 🌟 Modern Features Added

1. **TypeScript**: Full type safety across the application
2. **Form Validation**: Zod schemas with React Hook Form
3. **Component Library**: Reusable UI components with Radix UI
4. **Animation**: Smooth page transitions and micro-interactions
5. **SEO Optimization**: Next SEO for better search engine visibility
6. **Error Handling**: Comprehensive error boundaries and user feedback
7. **Mobile Sharing**: Native Web Share API integration
8. **Responsive Design**: Mobile-first responsive layout
9. **Performance**: Optimized with Next.js App Router
10. **Developer Experience**: ESLint, TypeScript, and modern tooling

---

**🎉 Modern Campus Ambassador Program - Ready for Production!**

This application is now fully modernized with the latest web technologies and best practices, making it production-ready and easily integrable into any existing website or platform.
